#!/bin/sh
set -e  # Exit immediately if a command exits with a non-zero status.

echo "Entrypoint: Fixing directory permissions..."
# Take ownership of the directories Next.js needs to write to.
chown -R node:node /home/<USER>/app/.next
chown -R node:node /home/<USER>/app/public

echo "Entrypoint: Handing off to user 'node'..."
# Execute the original command (e.g., "npm run dev") AS the 'node' user.
# 'gosu' is a lightweight 'sudo' alternative common in containers.
exec gosu node "$@"
