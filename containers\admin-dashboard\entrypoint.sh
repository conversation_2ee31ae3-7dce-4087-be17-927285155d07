#!/bin/sh
set -e  # Exit immediately if a command exits with a non-zero status.

echo "Entrypoint: Setting up directories and permissions..."

# Get the node user's UID and GID
NODE_UID=$(id -u node)
NODE_GID=$(id -g node)

echo "Node user UID: $NODE_UID, GID: $NODE_GID"

# Create directories if they don't exist
mkdir -p /home/<USER>/app/.next
mkdir -p /home/<USER>/app/public

# For named volumes, <PERSON><PERSON> creates them as root:root initially
# We need to change ownership if possible, or ensure they're writable
echo "Entrypoint: Attempting to fix directory permissions..."

# Check if we can change ownership (works for named volumes)
if chown node:node /home/<USER>/app/.next 2>/dev/null; then
    echo "Successfully changed ownership of .next directory"
    chown -R node:node /home/<USER>/app/.next
else
    echo "Could not change ownership of .next, trying to make it writable..."
    # If we can't change ownership, at least make it writable
    chmod 777 /home/<USER>/app/.next 2>/dev/null || echo "Warning: Could not make .next writable"
fi

# Handle public directory similarly
if chown node:node /home/<USER>/app/public 2>/dev/null; then
    echo "Successfully changed ownership of public directory"
    chown -R node:node /home/<USER>/app/public
else
    echo "Could not change ownership of public, trying to make it writable..."
    chmod 777 /home/<USER>/app/public 2>/dev/null || echo "Warning: Could not make public writable"
fi

echo "Entrypoint: Directory setup complete. Handing off to user 'node'..."
# Execute the original command (e.g., "npm run dev") AS the 'node' user.
# 'gosu' is a lightweight 'sudo' alternative common in containers.
exec gosu node "$@"
