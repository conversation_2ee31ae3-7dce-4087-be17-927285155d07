#!/bin/sh
# Exit immediately if a command exits with a non-zero status.
set -e

echo "Entrypoint: Ensuring .next directory exists and has correct permissions..."
# Create the .next directory if it doesn't exist.
mkdir -p /home/<USER>/app/.next
# As root, change the ownership of the .next directory to the node user.
chown -R node:node /home/<USER>/app/.next

echo "Entrypoint: Handing off to user 'node' to run the application..."
# Use 'gosu' to drop privileges and execute the main container command
# (e.g., "npm run dev") as the 'node' user.
exec gosu node "$@"
